import { reactive, computed } from 'vue'

/**
 * 下拉框管理 Hook
 * 提取下拉框的交互逻辑，分离业务逻辑和UI逻辑
 */
export function useDropdowns(onDataChange) {
  const data = reactive({
    chainDropdownVisible: false,
    regionDropdownVisible: false,
    selectedChainData: [],
    selectedRegionData: [],
    regionMode: 'nearby', // 'nearby' | 'national'
  })

  // 计算属性
  const selectedChain = computed(() => {
    return data.selectedChainData[0] || { name: '请选择产业链' }
  })

  const selectedRegion = computed(() => {
    return data.selectedRegionData[0] || { name: '全国' }
  })

  // 产业链下拉框相关方法
  const toggleChainDropdown = () => {
    data.chainDropdownVisible = !data.chainDropdownVisible
    data.regionDropdownVisible = false // 关闭地区下拉框
  }

  const handleChainDropdownClose = () => {
    data.chainDropdownVisible = false
  }

  const handleChainSubmit = result => {
    // ChainSelection 返回的数据格式: { selection: {...}, path: {...} }
    if (result && result.selection) {
      data.selectedChainData = [result.selection]
    }
    data.chainDropdownVisible = false

    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'chain',
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 地区模式切换
  const switchRegionMode = mode => {
    data.regionMode = mode
    data.regionDropdownVisible = false // 关闭下拉框

    if (mode === 'nearby') {
      // 切换到周边模式，重置地区选择
      data.selectedRegionData = []
    } else if (mode === 'national') {
      // 切换到全国模式，设置默认为全国
      data.selectedRegionData = [
        {
          code: 'All',
          name: '全国',
          level: '0',
        },
      ]
    }

    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'regionMode',
        mode: mode,
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 地区下拉框相关方法
  const toggleRegionDropdown = () => {
    // 只有在全国模式下才能打开下拉框
    if (data.regionMode === 'national') {
      data.regionDropdownVisible = !data.regionDropdownVisible
      data.chainDropdownVisible = false // 关闭产业链下拉框
    }
  }

  const handleRegionDropdownClose = () => {
    data.regionDropdownVisible = false
  }

  const handleRegionSubmit = result => {
    // RegionSelection 返回的数据格式: { selection: {...}, path: {...} }
    if (result && result.selection) {
      data.selectedRegionData = [result.selection]
    }
    data.regionDropdownVisible = false

    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'region',
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 初始化数据
  const initializeData = routeQuery => {
    const { code, name } = routeQuery
    if (code && name) {
      // 设置初始选中的产业链数据
      data.selectedChainData = [
        {
          chain_code: code,
          name: name,
        },
      ]
    }

    // 默认选择周边模式
    data.regionMode = 'nearby'
    data.selectedRegionData = []

    // 通知初始数据
    if (onDataChange) {
      onDataChange({
        type: 'init',
        mode: data.regionMode,
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 获取当前选择的数据
  const getCurrentSelection = () => {
    return {
      chainData: data.selectedChainData,
      regionData: data.selectedRegionData,
    }
  }

  return {
    // 响应式数据
    data,
    selectedChain,
    selectedRegion,

    // 产业链下拉框方法
    toggleChainDropdown,
    handleChainDropdownClose,
    handleChainSubmit,

    // 地区下拉框方法
    switchRegionMode,
    toggleRegionDropdown,
    handleRegionDropdownClose,
    handleRegionSubmit,

    // 工具方法
    initializeData,
    getCurrentSelection,
  }
}
