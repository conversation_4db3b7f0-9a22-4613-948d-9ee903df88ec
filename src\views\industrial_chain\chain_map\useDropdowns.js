import { reactive, computed } from 'vue'

/**
 * 下拉框管理 Hook
 * 提取下拉框的交互逻辑，分离业务逻辑和UI逻辑
 */
export function useDropdowns(onDataChange) {
  const data = reactive({
    chainDropdownVisible: false,
    regionDropdownVisible: false,
    selectedChainData: [],
    selectedRegionData: [],
  })

  // 计算属性
  const selectedChain = computed(() => {
    return data.selectedChainData[0] || { name: '请选择产业链' }
  })

  const selectedRegion = computed(() => {
    return data.selectedRegionData[0] || { name: '全国' }
  })

  // 产业链下拉框相关方法
  const toggleChainDropdown = () => {
    data.chainDropdownVisible = !data.chainDropdownVisible
    data.regionDropdownVisible = false // 关闭地区下拉框
  }

  const handleChainDropdownClose = () => {
    data.chainDropdownVisible = false
  }

  const handleChainSubmit = (result) => {
    // ChainSelection 返回的数据格式: { selection: {...}, path: {...} }
    if (result && result.selection) {
      data.selectedChainData = [result.selection]
    }
    data.chainDropdownVisible = false
    
    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'chain',
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 地区下拉框相关方法
  const toggleRegionDropdown = () => {
    data.regionDropdownVisible = !data.regionDropdownVisible
    data.chainDropdownVisible = false // 关闭产业链下拉框
  }

  const handleRegionDropdownClose = () => {
    data.regionDropdownVisible = false
  }

  const handleRegionSubmit = (result) => {
    // RegionSelection 返回的数据格式: { selection: {...}, path: {...} }
    if (result && result.selection) {
      data.selectedRegionData = [result.selection]
    }
    data.regionDropdownVisible = false
    
    // 通知数据变化
    if (onDataChange) {
      onDataChange({
        type: 'region',
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 初始化数据
  const initializeData = (routeQuery) => {
    const { code, name } = routeQuery
    if (code && name) {
      // 设置初始选中的产业链数据
      data.selectedChainData = [
        {
          chain_code: code,
          name: name,
        },
      ]
    }

    // 设置默认地区数据
    data.selectedRegionData = [
      {
        code: 'All',
        name: '全国',
        level: '0',
      },
    ]

    // 通知初始数据
    if (onDataChange) {
      onDataChange({
        type: 'init',
        chainData: data.selectedChainData,
        regionData: data.selectedRegionData,
      })
    }
  }

  // 获取当前选择的数据
  const getCurrentSelection = () => {
    return {
      chainData: data.selectedChainData,
      regionData: data.selectedRegionData,
    }
  }

  return {
    // 响应式数据
    data,
    selectedChain,
    selectedRegion,
    
    // 产业链下拉框方法
    toggleChainDropdown,
    handleChainDropdownClose,
    handleChainSubmit,
    
    // 地区下拉框方法
    toggleRegionDropdown,
    handleRegionDropdownClose,
    handleRegionSubmit,
    
    // 工具方法
    initializeData,
    getCurrentSelection,
  }
}
