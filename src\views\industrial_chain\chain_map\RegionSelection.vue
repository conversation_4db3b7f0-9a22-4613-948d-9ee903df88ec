<template>
  <van-popup
    v-model:show="data.visible"
    position="bottom"
    round
    close-on-popstate
    close-on-click-overlay
    :style="{ height: '712px' }"
  >
    <div class="region-selection">
      <!-- 头部 -->
      <div class="header">
        <div class="title">选择地区</div>
        <div class="close-btn" @click="close">×</div>
      </div>

      <!-- 容器保持固定高度 -->
      <div class="region-container">
        <!-- 占位内容 -->
        <div v-if="!data.readyToShow" class="placeholder-content">
          <div class="placeholder-text">地区数据加载中...</div>
        </div>

        <!-- 主要内容 - 只有在准备好时才显示 -->
        <div v-else class="region-wrap">
          <!-- 省 -->
          <div class="list province-list">
            <div class="scroll-container">
              <div
                v-for="item in data.regionObj.provinceList"
                :key="item.code"
                :class="['item', 'province', { active: item.active, selected: item.selected }]"
                @click="selectRegion(item)"
              >
                <span :class="{ 'selected-text': item.selected }">{{ item.name }}</span>
                <img
                  v-if="item.selected"
                  class="checkmark show"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
                  alt="选中"
                />
              </div>
            </div>
          </div>

          <!-- 市 -->
          <div class="list city-list">
            <div class="scroll-container">
              <div
                v-for="item in data.regionObj.curCityList"
                :key="item.code"
                :class="['item', 'city', { active: item.active, selected: item.selected }]"
                @click="selectRegion(item)"
              >
                <span :class="{ 'selected-text': item.selected }">{{ item.name }}</span>
                <img
                  v-if="item.selected"
                  class="checkmark show"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
                  alt="选中"
                />
              </div>
            </div>
          </div>

          <!-- 区 -->
          <div class="list area-list">
            <div class="scroll-container">
              <div
                v-for="item in data.regionObj.curAreaList"
                :key="item.code"
                :class="['item', 'area', { selected: item.selected }]"
                @click="selectRegion(item)"
              >
                <span :class="{ 'selected-text': item.selected }">{{ item.name }}</span>
                <img
                  v-if="item.selected"
                  class="checkmark show"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
                  alt="选中"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer">
        <div class="btn-group">
          <button class="btn reset-btn" @click="reset">重置</button>
          <button class="btn submit-btn" @click="submit">确定</button>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { reactive, watch, onMounted } from 'vue'
import { getData } from '@/components/Hunt/component/MultipleChanyeChoice/utils.js'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  defaultCode: {
    type: [String, Object, Array],
    default: '',
  },
  dataType: {
    type: String,
    default: 'DistrictAry',
  },
})

const emits = defineEmits(['close', 'submit'])

const data = reactive({
  visible: false,
  regionObj: {
    provinceList: [],
    curCityList: [],
    curAreaList: [],
  },
  cityObj: {},
  areaObj: {},
  selectedPath: {
    country: null,
    province: null,
    city: null,
    area: null,
  },
  finalSelection: null,
  readyToShow: false,
})

// 监听显示状态
watch(
  () => props.visible,
  visible => {
    data.visible = visible
    if (visible && props.defaultCode) {
      data.readyToShow = false
      const code = extractCodeFromDefaultCode(props.defaultCode)
      if (code) {
        expandToCode(code)
      } else {
        data.readyToShow = true
      }
    } else if (visible) {
      data.readyToShow = true
    }
  },
  { immediate: true }
)

// 监听默认代码变化
watch(
  () => props.defaultCode,
  code => {
    if (code && data.visible) {
      data.readyToShow = false
      const extractedCode = extractCodeFromDefaultCode(code)
      if (extractedCode) {
        expandToCode(extractedCode)
      } else {
        data.readyToShow = true
      }
    }
  }
)

/**
 * 从 defaultCode 中提取 code 值
 */
const extractCodeFromDefaultCode = defaultCode => {
  if (!defaultCode) return ''

  if (typeof defaultCode === 'string') {
    return defaultCode
  }

  if (Array.isArray(defaultCode)) {
    if (defaultCode.length === 0) return ''
    const firstItem = defaultCode[0]
    if (typeof firstItem === 'string') {
      return firstItem
    }
    if (typeof firstItem === 'object' && firstItem.code) {
      return firstItem.code
    }
    return ''
  }

  if (typeof defaultCode === 'object' && defaultCode.code) {
    return defaultCode.code
  }

  return ''
}

/**
 * 展开到指定代码
 */
const expandToCode = async targetCode => {
  if (!targetCode) return

  const { regionObj, cityObj, areaObj } = data
  let selectedPath = {
    province: null,
    city: null,
    area: null,
    country: null,
  }

  // 先确保有省级数据
  if (regionObj.provinceList.length === 0) {
    await getRegion()
  }

  // 查找目标code属于哪一级
  let targetLevel = ''
  let targetItem = null
  let parentCode = ''

  // 1. 先在省级中查找
  for (let province of regionObj.provinceList) {
    if (province.code === targetCode) {
      targetLevel = 'province'
      targetItem = province
      break
    }
  }

  // 2. 如果不在省级，需要遍历所有省的市级数据查找
  if (!targetLevel) {
    // 先检查已缓存的市级数据
    for (let provinceCode in cityObj) {
      for (let city of cityObj[provinceCode]) {
        if (city.code === targetCode) {
          targetLevel = 'city'
          targetItem = city
          parentCode = provinceCode
          break
        }
      }
      if (targetLevel) break
    }

    // 如果还没找到，加载所有省的市级数据进行查找
    if (!targetLevel) {
      for (let province of regionObj.provinceList) {
        if (province.code === 'All') continue
        if (!cityObj[province.code]) {
          await loadCityData(province.code)
        }
        for (let city of cityObj[province.code]) {
          if (city.code === targetCode) {
            targetLevel = 'city'
            targetItem = city
            parentCode = province.code
            break
          }
        }
        if (targetLevel) break
      }
    }
  }

  // 3. 如果不在市级，查找区级
  if (!targetLevel) {
    // 先检查已缓存的区级数据
    for (let cityCode in areaObj) {
      for (let area of areaObj[cityCode]) {
        if (area.code === targetCode) {
          targetLevel = 'area'
          targetItem = area
          parentCode = cityCode
          break
        }
      }
      if (targetLevel) break
    }

    // 如果还没找到，需要加载更多区级数据
    if (!targetLevel) {
      for (let provinceCode in cityObj) {
        for (let city of cityObj[provinceCode]) {
          if (city.isarea) continue
          if (!areaObj[city.code]) {
            await loadAreaData(city.code)
          }
          for (let area of areaObj[city.code]) {
            if (area.code === targetCode) {
              targetLevel = 'area'
              targetItem = area
              parentCode = city.code
              break
            }
          }
          if (targetLevel) break
        }
        if (targetLevel) break
      }
    }
  }

  // 根据找到的级别进行展开
  if (targetLevel === 'province') {
    selectedPath.province = targetItem
    await loadCityData(targetCode)
  } else if (targetLevel === 'city') {
    const province = regionObj.provinceList.find(p => p.code === parentCode)
    selectedPath.province = province
    selectedPath.city = targetItem
    await loadCityData(parentCode)
    if (!targetItem.isarea) {
      await loadAreaData(targetCode)
    }
  } else if (targetLevel === 'area') {
    let cityCode = parentCode
    let provinceCode = ''

    for (let pCode in cityObj) {
      if (cityObj[pCode].find(c => c.code === cityCode)) {
        provinceCode = pCode
        break
      }
    }

    const province = regionObj.provinceList.find(p => p.code === provinceCode)
    const city = cityObj[provinceCode]?.find(c => c.code === cityCode)

    selectedPath.province = province
    selectedPath.city = city
    selectedPath.area = targetItem

    await loadCityData(provinceCode)
    await loadAreaData(cityCode)
  }

  data.selectedPath = selectedPath
  data.finalSelection = getFinalSelection(selectedPath)
  data.readyToShow = true

  updateListSelections()
}

/**
 * 加载市级数据
 */
const loadCityData = async provinceCode => {
  const { cityObj, regionObj } = data
  if (!cityObj[provinceCode]) {
    const cityList = await getData({
      code: provinceCode,
      dataType: props.dataType,
    })
    // 处理直辖市的区级数据，标记为 isarea
    const processedCityList = cityList.map(item => {
      if (item.level === '3') {
        item.isarea = true
      }
      return item
    })
    cityObj[provinceCode] = processedCityList
  }
  regionObj.curCityList = cityObj[provinceCode] || []
}

/**
 * 加载区级数据
 */
const loadAreaData = async cityCode => {
  const { areaObj, regionObj } = data
  if (!areaObj[cityCode]) {
    const areaList = await getData({
      code: cityCode,
      dataType: props.dataType,
    })
    areaObj[cityCode] = areaList
  }
  regionObj.curAreaList = areaObj[cityCode] || []
}

/**
 * 更新列表选中状态
 */
const updateListSelections = () => {
  const { regionObj, selectedPath } = data

  regionObj.provinceList = regionObj.provinceList.map(item => ({
    ...item,
    selected: selectedPath.country?.code === item.code || selectedPath.province?.code === item.code,
    active: selectedPath.country?.code === item.code || selectedPath.province?.code === item.code,
  }))

  regionObj.curCityList = regionObj.curCityList.map(item => ({
    ...item,
    selected: selectedPath.city?.code === item.code,
    active: selectedPath.city?.code === item.code,
  }))

  regionObj.curAreaList = regionObj.curAreaList.map(item => ({
    ...item,
    selected: selectedPath.area?.code === item.code,
  }))
}

/**
 * 获取最终选择
 */
const getFinalSelection = (path = null) => {
  const selectedPath = path || data.selectedPath
  return selectedPath.area || selectedPath.city || selectedPath.province || selectedPath.country
}

/**
 * 选择地区
 */
const selectRegion = async item => {
  const { code, level, name } = item
  const isAreaFlag = item.isarea
  let { selectedPath, regionObj } = data

  if (code === 'All') {
    selectedPath.country = { code, level: '0', name }
    selectedPath.province = null
    selectedPath.city = null
    selectedPath.area = null
    regionObj.curCityList = []
    regionObj.curAreaList = []
  } else if (level === '1') {
    selectedPath.country = null
    selectedPath.province = { code, level, name }
    selectedPath.city = null
    selectedPath.area = null
    await loadCityData(code)
    regionObj.curAreaList = []
  } else if (level === '2') {
    selectedPath.city = { code, level, name }
    selectedPath.area = null
    await loadAreaData(code)
  } else if (level === '3' && isAreaFlag) {
    selectedPath.city = { code, level, name, isarea: true }
    selectedPath.area = null
    regionObj.curAreaList = []
  } else if (level === '3') {
    selectedPath.area = { code, level, name }
  }

  data.selectedPath = selectedPath
  data.finalSelection = getFinalSelection(selectedPath)
  updateListSelections()
}

/**
 * 获取地区数据
 */
const getRegion = async (code = '', level) => {
  const { regionObj, cityObj, areaObj } = data
  let temp = !level ? 'provinceList' : level === '1' ? 'curCityList' : 'curAreaList'

  const dataList = await getData({ code, dataType: props.dataType })
  let processedList = dataList.map(item => {
    if (level === '1' && item.level === '3') {
      item.isarea = true
    }
    return {
      ...item,
      selected: false,
      active: false,
    }
  })

  if (!level) {
    processedList.unshift({
      code: 'All',
      name: '全国',
      level: '0',
      selected: true,
      active: true,
    })

    data.selectedPath = {
      country: { code: 'All', level: '0', name: '全国' },
      province: null,
      city: null,
      area: null,
    }
    data.finalSelection = { code: 'All', level: '0', name: '全国' }
    data.readyToShow = true
  }

  regionObj[temp] = processedList

  if (level === '1') cityObj[code] = dataList
  if (level === '2') areaObj[code] = dataList
}

/**
 * 重置
 */
const reset = () => {
  data.selectedPath = {
    country: { code: 'All', level: '0', name: '全国' },
    province: null,
    city: null,
    area: null,
  }
  data.finalSelection = { code: 'All', level: '0', name: '全国' }
  data.regionObj = {
    provinceList: data.regionObj.provinceList.map(item => ({
      ...item,
      selected: item.code === 'All',
      active: item.code === 'All',
    })),
    curCityList: [],
    curAreaList: [],
  }
}

/**
 * 关闭
 */
const close = () => {
  reset()
  emits('close')
}

/**
 * 提交
 */
const submit = () => {
  const { finalSelection, selectedPath } = data
  if (!finalSelection) {
    alert('请选择地区')
    return
  }

  const selectionWithParent = { ...finalSelection }

  if (selectedPath.area) {
    selectionWithParent.parent = selectedPath.city?.code
  } else if (selectedPath.city) {
    selectionWithParent.parent = selectedPath.province?.code
  } else if (selectedPath.province) {
    selectionWithParent.parent = 'All'
  } else if (selectedPath.country) {
    selectionWithParent.parent = null
  }

  emits('submit', {
    selection: selectionWithParent,
    path: selectedPath,
  })
}

// 组件挂载时初始化
onMounted(() => {
  data.readyToShow = false
  getRegion()
})
</script>

<style lang="scss" scoped>
.region-selection {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    font-size: 40px;
    color: #999;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.region-container {
  height: 500px;
  width: 100%;
  position: relative;
  flex: 1;
}

.region-wrap {
  height: 100%;
  width: 100%;
  display: flex;
  font-weight: 400;
  font-size: 26px;
  color: #525665;
}

.region-wrap .list {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.scroll-container {
  height: 100%;
  overflow-y: auto;
}

.region-wrap .list .item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.2s;
}

.province-list {
  background-color: rgba(247, 247, 247, 0.6);
}

.province-list .item.active {
  background-color: #fff;
}

.province-list .item.selected {
  background-color: #fff;
  font-weight: 600;
  font-size: 26px;
  color: #e72410;
}

.city-list {
  background-color: #fff;
  position: relative;

  &::before {
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: #eeeeee;
    transform: scaleX(0.5);
  }
}

.city-list .item.selected {
  font-weight: 600;
  font-size: 26px;
  color: #e72410;
}

.area-list {
  background-color: #fff;
  position: relative;

  &::before {
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background: #eeeeee;
    transform: scaleX(0.5);
  }
}

.area-list .item.selected {
  font-weight: 600;
  font-size: 26px;
  color: #e72410;
}

.selected-text {
  color: #e72410 !important;
  font-size: 26px;
  font-weight: 600 !important;
}

.checkmark {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.checkmark.show {
  opacity: 1;
}

.placeholder-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 28px;
  color: #999;
}

.footer {
  padding: 20px;
  border-top: 1px solid #eee;
  background: #fff;
}

.btn-group {
  display: flex;
  gap: 20px;
}

.btn {
  flex: 1;
  height: 88px;
  border: none;
  border-radius: 8px;
  font-size: 32px;
  cursor: pointer;
  transition: all 0.2s;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;

  &:hover {
    background: #e5e5e5;
  }
}

.submit-btn {
  background: #07a6f0;
  color: #fff;

  &:hover {
    background: #0690d3;
  }
}
</style>
