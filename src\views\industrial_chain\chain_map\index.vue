<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden flex flex-col">
    <div ref="headRef" class="head">
      <!-- 右边下拉框 -->
      <div class="dropdown-container">
        <div class="dropdown-trigger" @click="toggleDropdown">
          <span>{{ selectedChain.name || '请选择产业链' }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.dropdownVisible ? 'rotate-180' : '']"
          />
        </div>
      </div>
      <div class="btn_group">
        <div>周边</div>
        <div class="active f-all-center">
          全国
          <!-- :class="[data.dropdownVisible ? 'rotate-180' : '']" -->
          <svg-icon name="arrow_d_w" class="ml-8 transition-all duration-300 w-20 h-20" />
        </div>
      </div>
    </div>
    <!-- 下面超出就局部滚动 -->
    <div>
      <!-- 地图 -->
      <div class="w-full h-534">
        <ChainMap></ChainMap>
      </div>
      <!-- 推荐企业名单取前十个 -->
      <div>
        <!-- <EntCard></EntCard> 渲染列表用这个 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { industryListTen, hotCount, getNodeList } from '@/api/iChain/index'
import ChainMap from './ChainMap.vue'
import EntCard from '../../../components/EntCard/index.vue'

const route = useRoute()
const data = reactive({
  dropdownVisible: false,
  selectedChainData: [],
})
const selectedChain = computed(() => {
  return data.selectedChainData[0] || { name: '请选择产业链' }
})
const toggleDropdown = () => {
  data.dropdownVisible = !data.dropdownVisible
}
onMounted(() => {
  const { code, name } = route.query
  if (code && name) {
    // 设置初始选中的产业链数据
    data.selectedChainData = [
      {
        chain_code: code,
        name: name,
      },
    ]
  }
})
//  0,2,4,6
//  0（不按地区分组）
//  2（按地区（省份）分组）
//  4 （按地区（市）分组）
//  6（按地区（区）分组）
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
    }
  }
  .btn_group {
    @apply flex;
    height: 56px;
    background: #f4f4f4;
    border-radius: 8px;
    overflow: hidden;
    font-weight: 400;
    font-size: 24px;
    color: #404040;
    div {
      min-width: 124px;
      height: 56px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        color: #fff;
      }
    }
  }
}
</style>
