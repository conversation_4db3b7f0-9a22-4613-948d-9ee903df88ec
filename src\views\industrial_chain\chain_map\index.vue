<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden flex flex-col">
    <div ref="headRef" class="head">
      <!-- 左边产业链下拉框 -->
      <div class="dropdown-container">
        <div
          class="dropdown-trigger"
          @click="toggleChainDropdown"
          :class="{ 'dropdown-actives': data.chainDropdownVisible }"
        >
          <span :class="[data.chainDropdownVisible ? 'text-[#07a6f0]' : '']">{{
            selectedChain.name || '请选择产业链'
          }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20 text-[#A0A5BA]"
            :class="[data.chainDropdownVisible ? 'rotate-180 text-[#07a6f0]' : '']"
          />
        </div>
      </div>
      <!-- 右边地区下拉框 -->
      <div class="btn_group">
        <div>周边</div>
        <div class="active f-all-center" @click="toggleRegionDropdown">
          {{ selectedRegion.name || '全国' }}
          <svg-icon
            name="arrow_d_w"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.regionDropdownVisible ? 'rotate-180' : '']"
          />
        </div>
      </div>
    </div>
    <!-- 下面超出就局部滚动 -->
    <div>
      <!-- 地图 -->
      <div class="w-full h-534">
        <ChainMap></ChainMap>
      </div>
      <!-- 推荐企业名单取前十个 -->
      <div>
        <!-- <EntCard></EntCard> 渲染列表用这个 -->
      </div>
    </div>

    <!-- 产业链下拉框 -->
    <ChainSelection
      :visible="data.chainDropdownVisible"
      :defaultCode="data.selectedChainData.length > 0 ? data.selectedChainData[0].chain_code : ''"
      :headHeight="headHeight"
      @close="handleChainDropdownClose"
      @submit="handleChainSubmit"
    />

    <!-- 地区下拉框 -->
    <RegionSelection
      :visible="data.regionDropdownVisible"
      :defaultCode="data.selectedRegionData.length > 0 ? data.selectedRegionData[0].code : 'All'"
      :headHeight="headHeight"
      @close="handleRegionDropdownClose"
      @submit="handleRegionSubmit"
    />
  </div>
</template>

<script setup>
import ChainMap from './ChainMap.vue'
import ChainSelection from './ChainSelection.vue'
import RegionSelection from './RegionSelection.vue'
import { useDropdowns } from './useDropdowns.js'

const route = useRoute()
const headRef = ref(null)

const headHeight = computed(() => {
  return headRef.value?.offsetHeight || 88
})
// 业务逻辑：获取地图热力数据
const getMapHot = async selectionData => {
  console.log('地图数据更新:', selectionData)
  // 这里可以调用API获取地图数据
  // const mapData = await someAPI(selectionData.chainData, selectionData.regionData)
}

// 使用下拉框 hook
const {
  data,
  selectedChain,
  selectedRegion,
  toggleChainDropdown,
  handleChainDropdownClose,
  handleChainSubmit,
  toggleRegionDropdown,
  handleRegionDropdownClose,
  handleRegionSubmit,
  initializeData,
} = useDropdowns(getMapHot)

onMounted(() => {
  // 初始化下拉框数据
  initializeData(route.query)
})
//  0,2,4,6
//  0（不按地区分组）
//  2（按地区（省份）分组）
//  4 （按地区（市）分组）
//  6（按地区（区）分组）
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
      .svg-left {
        color: red !important;
      }
    }
  }
  .btn_group {
    @apply flex;
    height: 56px;
    background: #f4f4f4;
    border-radius: 8px;
    overflow: hidden;
    font-weight: 400;
    font-size: 24px;
    color: #404040;
    div {
      min-width: 124px;
      height: 56px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        color: #fff;
      }
    }
  }
}
</style>
