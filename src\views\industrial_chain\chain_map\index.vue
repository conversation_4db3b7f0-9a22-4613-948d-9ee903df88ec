<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden flex flex-col">
    <div ref="headRef" class="head">
      <!-- 左边产业链下拉框 -->
      <div class="dropdown-container">
        <div class="dropdown-trigger" @click="toggleChainDropdown">
          <span>{{ selectedChain.name || '请选择产业链' }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.chainDropdownVisible ? 'rotate-180' : '']"
          />
        </div>
      </div>
      <!-- 右边地区下拉框 -->
      <div class="btn_group">
        <div>周边</div>
        <div class="active f-all-center" @click="toggleRegionDropdown">
          {{ selectedRegion.name || '全国' }}
          <svg-icon
            name="arrow_d_w"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.regionDropdownVisible ? 'rotate-180' : '']"
          />
        </div>
      </div>
    </div>
    <!-- 下面超出就局部滚动 -->
    <div>
      <!-- 地图 -->
      <div class="w-full h-534">
        <ChainMap></ChainMap>
      </div>
      <!-- 推荐企业名单取前十个 -->
      <div>
        <!-- <EntCard></EntCard> 渲染列表用这个 -->
      </div>
    </div>

    <!-- 产业链下拉框 -->
    <ChainDropdown
      :visible="data.chainDropdownVisible"
      :selectedData="data.selectedChainData"
      :headHeight="headHeight"
      @close="handleChainDropdownClose"
      @submit="handleChainSubmit"
    />

    <!-- 地区下拉框 -->
    <RegionSelection
      :visible="data.regionDropdownVisible"
      :defaultCode="data.selectedRegionData.length > 0 ? data.selectedRegionData[0].code : 'All'"
      :headHeight="headHeight"
      @close="handleRegionDropdownClose"
      @submit="handleRegionSubmit"
    />
  </div>
</template>

<script setup>
import ChainMap from './ChainMap.vue'
import ChainDropdown from '../chain_node/ChainDropdown.vue'
import RegionSelection from './RegionSelection.vue'

const route = useRoute()
const headRef = ref(null)

const data = reactive({
  chainDropdownVisible: false,
  regionDropdownVisible: false,
  selectedChainData: [],
  selectedRegionData: [],
})

const selectedChain = computed(() => {
  return data.selectedChainData[0] || { name: '请选择产业链' }
})

const selectedRegion = computed(() => {
  return data.selectedRegionData[0] || { name: '全国' }
})

const headHeight = computed(() => {
  return headRef.value?.offsetHeight || 88
})

// 产业链下拉框相关方法
const toggleChainDropdown = () => {
  data.chainDropdownVisible = !data.chainDropdownVisible
  data.regionDropdownVisible = false // 关闭地区下拉框
}

const handleChainDropdownClose = () => {
  data.chainDropdownVisible = false
}

const handleChainSubmit = selectedData => {
  data.selectedChainData = selectedData
  data.chainDropdownVisible = false
}

// 地区下拉框相关方法
const toggleRegionDropdown = () => {
  data.regionDropdownVisible = !data.regionDropdownVisible
  data.chainDropdownVisible = false // 关闭产业链下拉框
}

const handleRegionDropdownClose = () => {
  data.regionDropdownVisible = false
}

const handleRegionSubmit = result => {
  // RegionSelection 返回的数据格式: { selection: {...}, path: {...} }
  if (result && result.selection) {
    data.selectedRegionData = [result.selection]
  }
  data.regionDropdownVisible = false
}
onMounted(() => {
  const { code, name } = route.query
  if (code && name) {
    // 设置初始选中的产业链数据
    data.selectedChainData = [
      {
        chain_code: code,
        name: name,
      },
    ]
  }

  // 设置默认地区数据
  data.selectedRegionData = [
    {
      code: 'All',
      name: '全国',
      level: '0',
    },
  ]
})
//  0,2,4,6
//  0（不按地区分组）
//  2（按地区（省份）分组）
//  4 （按地区（市）分组）
//  6（按地区（区）分组）
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
    }
  }
  .btn_group {
    @apply flex;
    height: 56px;
    background: #f4f4f4;
    border-radius: 8px;
    overflow: hidden;
    font-weight: 400;
    font-size: 24px;
    color: #404040;
    div {
      min-width: 124px;
      height: 56px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        color: #fff;
      }
    }
  }
}
</style>
