<template>
  <div v-if="visible" class="dropdown-overlay" @click="close" :style="{ top: headHeight + 'px' }">
    <div class="dropdown-content" @click.stop>
      <div class="chain-wrap">
        <!-- 第一级：产业大类 -->
        <div class="parent">
          <div
            v-for="item in data.chainObj.parentList"
            :key="item.chain_code"
            @click="selectChain(item)"
            :class="{ item: true, active: item.active }"
          >
            <div class="name text-ellipsis">{{ item.name }}</div>
            <img
              v-if="item.selected && data.finalSelection && data.finalSelection.level === '1'"
              src="@/assets/image/cyl/i_gougou.png"
              class="check-icon"
            />
          </div>
        </div>
        <!-- 第二级：具体产业链 -->
        <div class="children flex-1">
          <div
            v-for="item in data.chainObj.curChildList"
            :key="item.chain_code"
            @click="select<PERSON>hain(item)"
            class="child-item text-ellipsis"
          >
            <span class="child-name" :class="{ active: item.selected }">{{ item.name }}</span>
            <img
              v-if="item.selected && data.finalSelection && data.finalSelection.level === '2'"
              src="@/assets/image/cyl/i_gougou.png"
              class="check-icon"
            />
          </div>
        </div>
        <!-- 第三级：产业链节点（如果有的话） -->
        <div class="children flex-1" v-if="data.chainObj.curNodeList.length > 0">
          <div
            v-for="item in data.chainObj.curNodeList"
            :key="item.chain_code"
            @click="selectChain(item)"
            class="child-item text-ellipsis"
          >
            <span class="child-name" :class="{ active: item.selected }">{{ item.name }}</span>
            <img
              v-if="item.selected && data.finalSelection && data.finalSelection.level === '3'"
              src="@/assets/image/cyl/i_gougou.png"
              class="check-icon"
            />
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer">
        <div class="btn-group">
          <button class="btn reset-btn" @click="reset">重置</button>
          <button class="btn submit-btn" @click="submit">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, onMounted } from 'vue'
import { getChainList, getChildNodeList } from '@/api/iChain/index'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  defaultCode: {
    type: [String, Object, Array],
    default: '',
  },
  headHeight: {
    type: Number,
    default: 88,
  },
})

const emits = defineEmits(['close', 'submit'])

const data = reactive({
  visible: false,
  chainObj: {
    parentList: [], // 第一级：产业大类
    curChildList: [], // 第二级：具体产业链
    curNodeList: [], // 第三级：产业链节点
  },
  childObj: {}, // 缓存子级数据
  nodeObj: {}, // 缓存节点数据
  selectedPath: {
    parent: null, // 选中的产业大类
    child: null, // 选中的具体产业链
    node: null, // 选中的产业链节点
  },
  finalSelection: null,
  readyToShow: false,
})

// 监听显示状态
watch(
  () => props.visible,
  visible => {
    data.visible = visible
    if (visible && props.defaultCode) {
      data.readyToShow = false
      const code = extractCodeFromDefaultCode(props.defaultCode)
      if (code) {
        expandToCode(code)
      } else {
        data.readyToShow = true
      }
    } else if (visible) {
      data.readyToShow = true
    }
  },
  { immediate: true }
)

/**
 * 从 defaultCode 中提取 code 值
 */
const extractCodeFromDefaultCode = defaultCode => {
  if (!defaultCode) return ''

  if (typeof defaultCode === 'string') {
    return defaultCode
  }

  if (Array.isArray(defaultCode)) {
    if (defaultCode.length === 0) return ''
    const firstItem = defaultCode[0]
    if (typeof firstItem === 'string') {
      return firstItem
    }
    if (typeof firstItem === 'object' && firstItem.chain_code) {
      return firstItem.chain_code
    }
    return ''
  }

  if (typeof defaultCode === 'object' && defaultCode.chain_code) {
    return defaultCode.chain_code
  }

  return ''
}

/**
 * 展开到指定代码
 */
const expandToCode = async targetCode => {
  if (!targetCode) return

  const { chainObj, childObj, nodeObj } = data
  let selectedPath = {
    parent: null,
    child: null,
    node: null,
  }

  // 先确保有第一级数据
  if (chainObj.parentList.length === 0) {
    await getChainData()
  }

  // 查找目标code
  let targetItem = null
  let targetLevel = ''

  // 在第一级中查找
  targetItem = chainObj.parentList.find(item => item.chain_code === targetCode)
  if (targetItem) {
    targetLevel = '1'
    selectedPath.parent = targetItem
  } else {
    // 在第二级中查找
    for (let parentCode in childObj) {
      targetItem = childObj[parentCode]?.find(item => item.chain_code === targetCode)
      if (targetItem) {
        targetLevel = '2'
        const parent = chainObj.parentList.find(p => p.chain_code === parentCode)
        selectedPath.parent = parent
        selectedPath.child = targetItem
        await loadChildData(parentCode)
        break
      }
    }

    // 如果还没找到，在第三级中查找
    if (!targetItem) {
      for (let childCode in nodeObj) {
        targetItem = nodeObj[childCode]?.find(item => item.chain_code === targetCode)
        if (targetItem) {
          targetLevel = '3'
          // 需要找到对应的父级和子级
          // 这里需要根据实际的数据结构来实现
          break
        }
      }
    }
  }

  data.selectedPath = selectedPath
  data.finalSelection = getFinalSelection(selectedPath)
  data.readyToShow = true

  updateListSelections()
}

/**
 * 获取产业链数据
 */
const getChainData = async () => {
  try {
    const res = await getChainList()
    const chainData = res || []
    console.log(222, res)

    // 处理数据格式，第一级是产业大类
    data.chainObj.parentList = chainData.map(item => ({
      ...item,
      level: '1',
      selected: false,
      active: false,
    }))

    data.readyToShow = true
  } catch (error) {
    console.error('获取产业链数据失败:', error)
    data.chainObj.parentList = []
    data.readyToShow = true
  }
}

/**
 * 加载子级数据（具体产业链）
 */
const loadChildData = async parentCode => {
  const { childObj, chainObj } = data
  if (!childObj[parentCode]) {
    // 从父级数据中获取子级
    const parent = chainObj.parentList.find(p => p.chain_code === parentCode)
    if (parent && parent.children) {
      childObj[parentCode] = parent.children.map(item => ({
        ...item,
        level: '2',
        selected: false,
        active: false,
      }))
    } else {
      childObj[parentCode] = []
    }
  }
  chainObj.curChildList = childObj[parentCode] || []
}

/**
 * 加载节点数据（产业链节点）
 */
const loadNodeData = async childCode => {
  const { nodeObj, chainObj } = data
  if (!nodeObj[childCode]) {
    // 这里可以调用API获取第三级数据
    // 暂时设为空数组，等有API时再实现
    nodeObj[childCode] = []
  }
  chainObj.curNodeList = nodeObj[childCode] || []
}

/**
 * 更新列表选中状态
 */
const updateListSelections = () => {
  const { chainObj, selectedPath } = data

  chainObj.parentList = chainObj.parentList.map(item => ({
    ...item,
    selected: selectedPath.parent?.chain_code === item.chain_code,
    active: selectedPath.parent?.chain_code === item.chain_code,
  }))

  chainObj.curChildList = chainObj.curChildList.map(item => ({
    ...item,
    selected: selectedPath.child?.chain_code === item.chain_code,
    active: selectedPath.child?.chain_code === item.chain_code,
  }))

  chainObj.curNodeList = chainObj.curNodeList.map(item => ({
    ...item,
    selected: selectedPath.node?.chain_code === item.chain_code,
  }))
}

/**
 * 获取最终选择
 */
const getFinalSelection = (path = null) => {
  const selectedPath = path || data.selectedPath
  return selectedPath.node || selectedPath.child || selectedPath.parent
}

/**
 * 选择产业链
 */
const selectChain = async item => {
  const { chain_code, level, name } = item

  if (level === '1') {
    // 选择第一级
    data.selectedPath.parent = item
    data.selectedPath.child = null
    data.selectedPath.node = null

    // 加载第二级数据
    await loadChildData(chain_code)
    data.chainObj.curNodeList = []
  } else if (level === '2') {
    // 选择第二级
    data.selectedPath.child = item
    data.selectedPath.node = null

    // 加载第三级数据
    await loadNodeData(chain_code)
  } else if (level === '3') {
    // 选择第三级
    data.selectedPath.node = item
  }

  data.finalSelection = getFinalSelection()
  updateListSelections()
}

/**
 * 重置选择
 */
const reset = () => {
  data.selectedPath = {
    parent: null,
    child: null,
    node: null,
  }
  data.finalSelection = null
  data.chainObj.curChildList = []
  data.chainObj.curNodeList = []
  updateListSelections()
}

/**
 * 提交选择
 */
const submit = () => {
  const selection = getFinalSelection()
  if (selection) {
    emits('submit', {
      selection,
      path: data.selectedPath,
    })
  }
}

/**
 * 关闭弹窗
 */
const close = () => {
  emits('close')
}

// 页面挂载时获取数据
onMounted(() => {
  getChainData()
})
</script>

<style lang="scss" scoped>
.dropdown-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.dropdown-content {
  width: 100vw;
  max-width: 100vw;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.chain-wrap {
  background-color: #fff;
  display: flex;
  height: 756px;
}

.parent {
  width: 308px;
  height: 756px;
  background-color: rgba(247, 247, 247, 0.6);
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.item {
  min-height: 84px;
  padding: 16px 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item.active {
  background-color: #fff;
  font-family: PingFang SC-Semibold, PingFang SC;
  .name {
    font-size: 26px;
    font-weight: 600;
    color: #07a6f0 !important;
  }
}

.item .name {
  width: 240px;
  font-weight: 400;
  font-size: 26px;
  color: #404040;
}

.text-ellipsis {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.2;
}

// 子级项目样式
.child-item {
  min-height: 84px;
  padding: 16px 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  .check-icon {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    margin-left: 8px;
  }
  .child-name {
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.3;

    &.active {
      color: #07a6f0 !important;
      font-weight: 600;
      font-family: PingFang SC-Semibold, PingFang SC;
    }
  }
}

.children {
  height: 756px;
  top: 0;
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.check-icon {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.footer {
  padding: 20px 24px;
  background: #fff;
  border-top: 1px solid #eee;
}

.btn-group {
  display: flex;
  gap: 16px;
}

.btn {
  flex: 1;
  height: 80px;
  border: none;
  border-radius: 8px;
  font-size: 28px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;

  &:hover {
    background: #e8e8e8;
  }
}

.submit-btn {
  background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
  color: #fff;

  &:hover {
    opacity: 0.9;
  }
}
</style>
